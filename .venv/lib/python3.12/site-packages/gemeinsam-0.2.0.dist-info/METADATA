Metadata-Version: 2.1
Name: gemeinsam
Version: 0.2.0
Summary: Biome Commons
Home-page: https://git.biome.io
Author: <PERSON>
License: MIT License
Requires-Python: >=3.10
Description-Content-Type: text/markdown
Requires-Dist: azure-identity <2.0.0,>=1.14.0
Requires-Dist: azure-keyvault-secrets <5.0.0,>=4.0.0
Requires-Dist: azure-eventgrid <5.0.0,>=4.0.0

# Gemeinsam

Biome Commons

* [gemeinsam.configloader](#module-gemeinsamconfigloader) Loads configuration for different configuration environments.
* [gemeinsam.decorators](#module-gemeinsamdecorators) Different utility functions as decorators.
* [gemeinsam.types](#module-gemeinsamtypes) Common functions related to types and their conversion.
* [gemeinsam.radio](#module-gemeinsamradio) Azure Event Grid client to publish events.



# Module `gemeinsam.configloader`


### Class `ConfigLoader`
>     class ConfigLoader(
>         config_data: dict,
>         active_env_key='ACTIVE_ENV',
>         vaulturi_key='vault_uri',
>         prod_env_label='prod',
>         test_env_label='local'
>     )

Loads configuration data from a nested dictionary. Each configuration
property is either accessible directly as a property or as a
secret value that is either pulled from the underlying envirnoment
variable or from the keyvault.

The configuration dict object can store conigurations for diffrent
environments. Active environment is selected based on `ACTIVE_ENV`
environment variable.

Secrets are loaded from Azure key vault. Its URL is picked from
`vault_uri` property as defined in the configuration.

For example::

    config_data = {
        'default': {
            'key1': 'some value',
            'key2': 'SOME_ENV_VAR',
        }
    }
    config = ConfigLoader(config_data, test_env_label='default')
    ...
    # Later in the code we can refer this configuration as follows:
    config.key1
    config.secret('key2')


##### Variable `active_env`

Active environment label.


    
##### Method `is_prod`
>     def is_prod(self) ‑> bool

Check if active environment is production environment.


##### Method `reload`
>     def reload(self, config_data:dict)

Reloads the configuration data from the dictionary.

    
##### Method `secret`
>     def secret(self, key, default=None)

Given a key in the configuration, pull secret value
from keyvault or from the environment variable.


##### Method `update`
>     def update(self, key: str, value)

Change a configuration in the active environment at runtimne.



# Module `gemeinsam.decorators`

    
### Function `timeit`
>     def timeit(f)

Decorator to log function execution time.




# Module `gemeinsam.types`


### Variable `mime_json`
JSON mime/type.


### Variable `pacific`
US/Pacific tzinfo object.

    
### Function `epoch`
>     def epoch(dt=None)

Converts a datetime into epoch time. If none given, uses current date time.

    
### Function `parse_isodate`
>     def parse_isodate(dtstr)

Parses an ISO formatted date or datetime string into date only object.
Throws `ValueError` if date string is not correctly formatted.

    
### Function `sencode`
>     def sencode(d)

Takes any primitive or generator type object and returns a
strictly **string-based json encoded** copy. Caters for bytes,
dates, and nulls.

    
### Function `sqlescape`
>     def sqlescape(sql: str)

Escapes given SQL statement to avoid SQL injection attack.



# Module `gemeinsam.radio`

    
### Class `AegRadio`
>     class AegRadio(
>         topic_endpoint,
>         topic_key=None
>     )

Azure Event Grid as a transmitter to announce events information


##### Variable `aeg_client`

Returns event grid publisher client.


##### Method `broadcast`
>     def broadcast(
>         self,
>         event_type: str,
>         subject: str,
>         data: dict
>     )

Broadcast the event if topic endpoint is defined.
