Metadata-Version: 2.4
Name: opentelemetry-exporter-otlp-proto-grpc
Version: 1.34.1
Summary: OpenTelemetry Collector Protobuf over gRPC Exporter
Project-URL: Homepage, https://github.com/open-telemetry/opentelemetry-python/tree/main/exporter/opentelemetry-exporter-otlp-proto-grpc
Project-URL: Repository, https://github.com/open-telemetry/opentelemetry-python
Author-email: OpenTelemetry Authors <<EMAIL>>
License: Apache-2.0
License-File: LICENSE
Classifier: Development Status :: 5 - Production/Stable
Classifier: Framework :: OpenTelemetry
Classifier: Framework :: OpenTelemetry :: Exporters
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: Apache Software License
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Requires-Python: >=3.9
Requires-Dist: googleapis-common-protos~=1.52
Requires-Dist: grpcio<2.0.0,>=1.63.2; python_version < '3.13'
Requires-Dist: grpcio<2.0.0,>=1.66.2; python_version >= '3.13'
Requires-Dist: opentelemetry-api~=1.15
Requires-Dist: opentelemetry-exporter-otlp-proto-common==1.34.1
Requires-Dist: opentelemetry-proto==1.34.1
Requires-Dist: opentelemetry-sdk~=1.34.1
Requires-Dist: typing-extensions>=4.5.0
Description-Content-Type: text/x-rst

OpenTelemetry Collector Protobuf over gRPC Exporter
===================================================

|pypi|

.. |pypi| image:: https://badge.fury.io/py/opentelemetry-exporter-otlp-proto-grpc.svg
   :target: https://pypi.org/project/opentelemetry-exporter-otlp-proto-grpc/

This library allows to export data to the OpenTelemetry Collector using the OpenTelemetry Protocol using Protobuf over gRPC.

Installation
------------

::

     pip install opentelemetry-exporter-otlp-proto-grpc


References
----------

* `OpenTelemetry Collector Exporter <https://opentelemetry-python.readthedocs.io/en/latest/exporter/otlp/otlp.html>`_
* `OpenTelemetry Collector <https://github.com/open-telemetry/opentelemetry-collector/>`_
* `OpenTelemetry <https://opentelemetry.io/>`_
* `OpenTelemetry Protocol Specification <https://github.com/open-telemetry/oteps/blob/main/text/0035-opentelemetry-protocol.md>`_
