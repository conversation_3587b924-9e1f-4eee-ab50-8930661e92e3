# -*- coding: utf-8 -*-
"""
Azure Event Grid client to publish events.

Created on 2024-03-24

@author: khalid
"""
import logging
from abc import ABC, abstractmethod
from azure.core.credentials import AzureKeyCredential
from azure.identity import DefaultAzureCredential
from azure.eventgrid import EventGridPublisherClient, EventGridEvent


class Radio(ABC):
    """
    Abstract radio to announce events information.
    """

    @abstractmethod
    def broadcast(self, event_type: str, subject: str, data: dict):
        raise NotImplementedError(
            'Abstract method `broadcast` not implemented.'
        )


class AegRadio(Radio):
    """
    Azure Event Grid as a transmitter to announce events information
    """

    _aeg_client: EventGridPublisherClient = None

    def __init__(self, topic_endpoint, topic_key=None):
        self.topic_endpoint = topic_endpoint
        self.topic_key = topic_key

    @property
    def aeg_client(self):
        """ Returns event grid publisher client. """
        if self._aeg_client is None:
            if self.topic_key:
                credential = AzureKeyCredential(self.topic_key)
            else:
                credential = DefaultAzureCredential()
            self._aeg_client = EventGridPublisherClient(
                self.topic_endpoint,
                credential)
            logging.debug(f'Connected to topic {self.topic_endpoint}')
        return self._aeg_client

    def broadcast(self, event_type: str, subject: str, data: dict):
        """ Broadcast the event if topic endpoint is defined. """
        if not self.topic_endpoint:
            logging.warning('No topic endpoint defined, can not broadcast!')
            return
        try:
            self.aeg_client.send([
                EventGridEvent(
                    event_type=event_type,
                    subject=subject,
                    data=data,
                    data_version="2.0"
                )
            ])
            logging.debug(f'Event published - {event_type}')
        except Exception as exp:
            logging.error(
                'Can not broadcast event due to exception',
                exc_info=exp,
            )
