# -*- coding: utf-8 -*-
"""
Common functions related to types and their conversion.

Created on 2024-03-24

@author: khalid
"""
import mimetypes
from types import GeneratorType
from datetime import datetime, date
from zoneinfo import ZoneInfo


pacific = ZoneInfo('US/Pacific')
""" US/Pacific tzinfo object. """


mime_json = mimetypes.types_map['.json']
""" JSON mime/type. """


def epoch(dt=None):
    """
    Converts a datetime into epoch time. If none given, uses current date time.
    """
    if dt is None:
        dt = datetime.now(tz=pacific)

    if isinstance(dt, str):
        dt = datetime.fromisoformat(dt)

    if not isinstance(dt, datetime):
        raise ValueError('Unsupported datetime object')

    if not dt.tzinfo:
        dt = dt.replace(tzinfo=pacific)

    return int((dt - datetime(1970, 1, 1, tzinfo=pacific)).total_seconds())


def parse_isodate(dtstr):
    """
    Parses an ISO formatted date or datetime string into date only object.
    Throws `ValueError` if date string is not correctly formatted.
    """

    _error_msg = 'Invalid date format'

    def str2int(segment):
        try:
            return int(segment)
        except ValueError:
            raise ValueError(_error_msg)

    if isinstance(dtstr, datetime):
        return dtstr.date()

    if isinstance(dtstr, date):
        return dtstr

    if dtstr is None or len(dtstr) == 0:
        return None

    # 4-digit year
    year = str2int(dtstr[0:4])
    if dtstr[4] not in ['-', '/']:
        raise ValueError(_error_msg)

    # 2-digit month
    month = str2int(dtstr[5:7])
    if dtstr[7] not in ['-', '/']:
        raise ValueError(_error_msg)

    # 2-digit day
    day = str2int(dtstr[8:10])
    # Fix: Data may contain text with zero as day value
    # e.g. 1900-01-00 -> so change it to 1900-01-01
    day = day if day > 0 else 1

    return date(year, month, day)


def sencode(d):
    """
    Takes any primitive or generator type object and returns a
    strictly **string-based json encoded** copy. Caters for bytes,
    dates, and nulls.
    """
    if isinstance(d, (tuple, list, set, GeneratorType)):
        return [sencode(x) for x in d]
    elif isinstance(d, bytes):
        return d.decode()
    elif isinstance(d, dict):
        _d = {}
        for k, v in d.items():
            _d[k] = sencode(v)
        return _d
    elif isinstance(d, (int, float)):
        return d
    elif d is None:
        return ''
    else:
        return str(d)


def sqlescape(sql: str):
    """ Escapes given SQL statement to avoid SQL injection attack. """
    return sql.translate(
        sql.maketrans({
            "\0": "\\0",
            "\r": "\\r",
            "\x08": "\\b",
            "\x09": "\\t",
            "\x1a": "\\z",
            "\n": "\\n",
            "\"": "",
            "'": "",
            "\\": "\\\\",
            "%": "\\%"
        }))
