# -*- coding: utf-8 -*-
"""
Different utility functions as decorators.

Created on 2024-03-24

@author: khalid
"""
import logging
from functools import wraps
from time import perf_counter


def timeit(f):
    """ Decorator to log function execution time. """
    @wraps(f)
    def wrapped(*args, **kw):
        start = perf_counter()
        result = f(*args, **kw)
        exec_time = perf_counter() - start
        logging.debug(
            f'Function {f.__module__}.{f.__name__} executed in {exec_time} sec'
        )
        return result
    return wrapped
