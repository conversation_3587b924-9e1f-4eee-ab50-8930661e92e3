# -*- coding: utf-8 -*-
"""
Loads configuration for different configuration environments.

Created on 2024-03-26

@author: khalid
"""
import os
from functools import cache, cached_property
from typing import Optional
from azure.core.exceptions import HttpResponseError
from azure.identity import DefaultAzureCredential
from azure.keyvault.secrets import SecretClient


class ConfigLoader:
    """
    Loads configuration data from a nested dictionary. Each configuration
    property is either accessible directly as a property or as a
    secret value that is either pulled from the underlying envirnoment
    variable or from the keyvault.

    The configuration dict object can store conigurations for diffrent
    environments. Active environment is selected based on `ACTIVE_ENV`
    environment variable.

    Secrets are loaded from Azure key vault. Its URL is picked from
    `vault_uri` property as defined in the configuration.

    For example::

        config_data = {
            'default': {
                'key1': 'some value',
                'key2': 'SOME_ENV_VAR',
            }
        }
        config = ConfigLoader(config_data, test_env_label='default')
        ...
        # Later in the code we can refer this configuration as follows:
        config.key1
        config.secret('key2')

    """

    __config_data: dict = None

    def __init__(
            self,
            config_data: dict,
            active_env_key='ACTIVE_ENV',
            vaulturi_key='vault_uri',
            prod_env_label='prod',
            test_env_label='local',

    ):
        self.__config_data = config_data
        self._env_key = active_env_key
        self._vault_uri = vaulturi_key
        self.prod_env_label = prod_env_label
        self.test_env_label = test_env_label

    def reload(self, config_data: dict):
        """ Reloads the configuration data from the dictionary."""
        self.__config_data = config_data

    @cached_property
    def vault(self) -> Optional[SecretClient]:
        """ Setup keyvault client."""
        vault_uri = self._active_config.get(self._vault_uri)
        if vault_uri:
            return SecretClient(vault_uri, credential=DefaultAzureCredential())
        return None

    @cached_property
    def active_env(self) -> str:
        """ Active environment label."""
        return os.getenv(self._env_key, self.test_env_label)

    def is_prod(self) -> bool:
        """
        Check if active environment is production environment.
        """
        return self.prod_env_label in self.active_env.lower()

    @property
    def _active_config(self) -> dict:
        """
        Configuration settting applicable in the active environment.
        """
        return self.__config_data[self.active_env]

    def update(self, key: str, value):
        """
        Change a configuration in the active environment at runtimne.
        """
        self._active_config[key] = value

    @cache
    def secret(self, key, default=None):
        """
        Given a key in the configuration, pull secret value
        from keyvault or from the environment variable.
        """
        kv_key = self._active_config.get(key)
        if kv_key and len(kv_key) > 0:
            if self.vault:
                try:
                    return self.vault.get_secret(kv_key).value
                except HttpResponseError:
                    pass
            # Try from environment
            return os.getenv(kv_key, default)
        return default

    def __getattr__(self, key):
        """
        Given a key, find value as defined in the active environemnt.
        """
        return self._active_config.get(key)
