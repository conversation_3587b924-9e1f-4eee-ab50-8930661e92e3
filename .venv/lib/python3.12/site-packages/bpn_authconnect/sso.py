# -*- coding: utf-8 -*-
"""
Created on 2020-07-21

@author: khalid
"""
import urllib.parse
from flask import (
    Blueprint,
    redirect,
    request
)
from bpn_authconnect import authcli
from bpn_authconnect import BPN_URL, RETURN_URL_KEY, insession

""" BPN trusted page used for login and logout. """
BPN_LOGIN_ENDPOINT = f"{BPN_URL}/login"
BPN_LOGOUT_ENDPOINT = f"{BPN_URL}/logout"


""" ADFS SSO routes """
bp = Blueprint('sso', __name__)


@bp.route('/login', methods=['GET'])
def login():
    """ This route is invoked by other routes in the app to trigger ADFS login. """
    # Prepares BPN login URL.
    # if RETURN_URL_KEY is in argument use RETURN_URL in params
    return_url = request.args.get(RETURN_URL_KEY)
    if return_url is None:
        return_url = f"{request.scheme}://{request.host}"
    params = {
        'wa': 'wsignin1.0',  # Must be this literal string (for future)
        'wtrealm': return_url,
        # Base URL of security realm of the relying party (for future)
        'returnUrl': return_url
        # bpn will redirect to this url with token to this url
    }
    # Collect error from QS and if available, forward it to next url
    error = request.args.get('error', '')
    if error != '':
        params["error"] = error
    # Encode '/' and other characters except : to send as query param in the URL
    qs = urllib.parse.urlencode(params, safe=':')
    # Finally, append query param with the main endpoint
    login_url = f'{BPN_LOGIN_ENDPOINT}?{qs}'
    return redirect(login_url)


@bp.route('/logout', methods=['GET'])
def logout():
    """ This route is invoked by other routes in the app when ADFS logout is requested. """
    # Clean up session
    authcli.clear_session()
    return redirect('/login')


@bp.route('/callback', methods=['GET'])
@insession
def bpn_callback():
    """ Trusted endpoint: invoked by SSO provider after user has logged in. """
    pass
