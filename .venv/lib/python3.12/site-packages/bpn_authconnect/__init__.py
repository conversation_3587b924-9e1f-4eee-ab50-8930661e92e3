# -*- coding: utf-8 -*-
"""
Created on 2020-03-24

@author: khalid
"""
import os
import datetime
import logging
import urllib
from functools import wraps
import json
import requests
import jwt
from flask import current_app as app, g, request, session, redirect

from werkzeug.exceptions import (
    Unauthorized,
    BadRequest,
    ServiceUnavailable,
    Forbidden
)
import backoff
from cryptography.hazmat.primitives import serialization
from azure.identity import DefaultAzureCredential
from azure.core.exceptions import (
    ClientAuthenticationError,
    ResourceNotFoundError
)
from azure.keyvault.secrets import SecretClient
from graphqlclient import GraphQLClient
from bpn_authconnect.cache import cache_result


""" Authorization header key """
HKEY_AUTH = 'Authorization'

""" Session key for storing user name. """
SKEY_USER = 'username'

""" Session key for storing access token. """
SKEY_ACCESS_TOKEN = 'access_token'

""" Session key for storing refresh token. """
SKEY_REFRESH_TOKEN = 'refresh_token'

""" Session key for last active time. """
LAST_ACTIVE = 'last_active'

""" Session key for storing token creation time. """
TOKEN_START_TIME = 'TOKEN_START_TIME'


BPN_URL = os.getenv('BPN_URL', 'https://bifrost.dev.biomedata.io')
RETURN_URL_KEY = "returnUrl"

""" Idle timeout. """
SESSION_EXPIRY_SECONDS_DEFAULT = 1800
SESSION_EXPIRY_SECONDS = os.getenv("SESSION_EXPIRY_SECONDS", "")
SESSION_EXPIRY_SECONDS = int(
    SESSION_EXPIRY_SECONDS) if SESSION_EXPIRY_SECONDS and str(
    SESSION_EXPIRY_SECONDS).isnumeric() else SESSION_EXPIRY_SECONDS_DEFAULT

""" token timeout. """
TOKEN_EXPIRY_SECONDS_DEFAULT = 900
TOKEN_EXPIRY_SECONDS = os.getenv("TOKEN_EXPIRY_SECONDS", "")
TOKEN_EXPIRY_SECONDS = int(
    TOKEN_EXPIRY_SECONDS) if TOKEN_EXPIRY_SECONDS and str(
    TOKEN_EXPIRY_SECONDS).isnumeric() else TOKEN_EXPIRY_SECONDS_DEFAULT


class AuthCli(object):
    ignore_list = ['YES', 'yes', 'Yes', 'TRUE', 'True', 1, True]

    def __init__(self):
        self._secret = None
        self.vault_name = os.getenv('VAULT_NAME', 'biome-apps-keyvault')
        odin_url = self.api_url if self.api_url.startswith(
            'https://odin') else f'{self.api_url}/api/odin'
        self.gql_client = GraphQLClient(f"{odin_url}/graphql")

    @property
    def secret(self):
        if self._secret is None:
            try:
                credential = DefaultAzureCredential()
                # noinspection PyTypeChecker
                client = SecretClient(
                    f'https://{self.vault_name}.vault.azure.net',
                    credential=credential
                )
                public_key_str = client.get_secret('auth-key-public').value
                self._secret = serialization.load_pem_public_key(
                    str.encode(public_key_str)
                )
            except (
                    ClientAuthenticationError,
                    ResourceNotFoundError,
                    ValueError
            ):
                pass
        return self._secret

    @property
    def api_url(self):
        """ Lazy load Auth API uri from the environment """
        # TODO Change annotation to cached_property when upgrading to Python 3.8
        url = BPN_URL
        return url

    def verify_jwt(self, token):
        try:
            payload = jwt.decode(token, self.secret, algorithms=['RS256'])
        except jwt.ExpiredSignatureError:
            # Alert expired token
            raise Unauthorized('Bearer token expired long ago!')
        except (jwt.InvalidSignatureError, jwt.DecodeError) as exp:
            logging.warning(str(exp))
            raise Unauthorized('Can not verify bearer!')
        return payload['sub']

    @cache_result(expiry=30)
    @backoff.on_exception(
        backoff.expo, (
                requests.exceptions.HTTPError,
                requests.exceptions.Timeout
        ),
        max_tries=3
    )
    def check_privilege(self, token, object_type, privilege, action):
        variables = {
            "policyInputs": [
                {
                    "resource": privilege,
                    "action": action,
                    "objectType": object_type
                }
            ]
        }
        query = """
                query($policyInputs: [PolicyRequest!]!){
                  checkPolicies(policyInputs: $policyInputs) {
                    eft
                  }
                }
            """
        self.gql_client.inject_token(f"Bearer {token}")
        response = self.gql_client.execute(query, variables)
        if "ERROR" in response.upper():
            raise BadRequest(response)
        response = json.loads(response)
        return 'allow' in response.get('data').get('checkPolicies')[0].get('eft')

    def refresh_access_token(self):
        refresh_token = session[SKEY_REFRESH_TOKEN]
        if not refresh_token:
            raise Forbidden("Refresh token not found in session")
        response = requests.get(
            f"{self.api_url}/auth/token",
            headers=bearer_header(refresh_token)
        )
        if response.status_code == 200:
            token = response.json().get("accessToken")
            self.init_session(access_token=token)
        else:
            raise Forbidden(f"{response.status_code}: {response.text}")

    def verify_token(
            self,
            token=None,
            object_type='General',
            privilege=None,
            action='read'
    ):
        """
        Verify bearer token.
        token: Bearer token.
        object_type: (Optional) object type to check privilege from, default General.
        privilege: (Optional) privilege name.
        action: (Optional) action name for authorization, default read.
        :return: username as returned by the Auth API or raises BadRequest or Unauthorized.
        """
        if app.config.get('TESTING', False) in self.ignore_list:
            # Ignore when running in test mode
            return
        # Read token value from session if present
        if token is None:
            token = session[SKEY_ACCESS_TOKEN]
        response = self.verify_jwt(token)
        if not privilege:
            return response
        else:
            if self.check_privilege(
                    token,
                    object_type,
                    privilege,
                    action
            ):
                return response
            else:
                raise Unauthorized(f"User unauthorized for {privilege}")

    @classmethod
    def init_session(cls, **kwargs):
        """ Set username and access token in the user session. """
        token = kwargs.get('access_token')
        username = kwargs.get('username')
        refresh_token = kwargs.get('refresh_token')

        if token:
            session[SKEY_ACCESS_TOKEN] = token
            session[TOKEN_START_TIME] = datetime.datetime.now().timestamp()
        if username:
            session[SKEY_USER] = username
        if refresh_token:
            session[SKEY_REFRESH_TOKEN] = refresh_token
        session[LAST_ACTIVE] = datetime.datetime.now().timestamp()

    @classmethod
    def clear_session(cls):
        """ Clears user session. """
        session.clear()
        for key in list(session.keys()):
            session.pop(key)


authcli = AuthCli()
verify_token = authcli.verify_token


def bearer_header(token):
    """ Prepare bearer auth header. """
    return {
        HKEY_AUTH: token if token.startswith('Bearer') else f'Bearer {token}'
    }


def __debug_mode():
    """
    Check if app is running in a debug mode for testing.
    """
    if app.config.get('TESTING', False) in authcli.ignore_list:
        authcli.init_session(username='test', access_token='')
        g.user = 'test'
        return True
    return False


def remove_jwt_from_url(url):
    # Remove JWT from query string
    parsed_url = urllib.parse.urlparse(url)
    query_params = urllib.parse.parse_qs(parsed_url.query)
    query_params.pop('jwt', None)
    new_query_string = urllib.parse.urlencode(query_params, doseq=True)
    new_url = urllib.parse.urlunparse(
        parsed_url._replace(query=new_query_string))
    return new_url


def init_jwt_session():
    result = ''
    param_jwt = request.args.get('jwt', '')
    # read error
    error = request.args.get('error', '')
    if param_jwt == '' and error != '':
        result = error
    if param_jwt != '':
        data = json.loads(param_jwt)
        access_token = data.get('accessToken')
        refresh_token = data.get('refreshToken')
        username = authcli.verify_token(access_token)
        authcli.init_session(username=username,
                             refresh_token=refresh_token,
                             access_token=access_token)
    return result


def authenticate(object_type='General', privilege=None, action='read'):
    """
    Annotation for REST endpoints to authenticate and authorize incoming requests.
    Uses either existing session or bearer token in the request headers or `token`
    or `access_token` query parameter in the same order.
    Set `app.config['TESTING'] = True` to ignore auth while running unit tests.
    """

    def decorator(f):
        @wraps(f)
        def view(*args, **kwargs):
            # No auth during testing
            if __debug_mode():
                return f(*args, **kwargs)
            # Read access token from the auth header
            token = request.headers.get(HKEY_AUTH, '').replace('Bearer ', '')
            # Finally, try query parameters
            if len(token) == 0:
                token = request.args.get(
                    'token',
                    request.args.get('access_token', None)
                )
            if token is None:
                # Check existing session
                if SKEY_USER in session:
                    time_span = datetime.datetime.now().timestamp() - session.get(
                        TOKEN_START_TIME,
                        datetime.datetime.now().timestamp()
                    )
                    if time_span <= TOKEN_EXPIRY_SECONDS:
                        g.user = session[SKEY_USER]
                        return f(*args, **kwargs)
                logging.warning(
                    f'NO BEARER - {request.url} -> {str(request.headers)}'
                )
                raise Unauthorized('No bearer token in the request')
            try:
                user = verify_token(token, object_type, privilege, action)
                authcli.init_session(username=user, access_token=token)
                # Set username in Flask globals for this request
                g.user = user
                return f(*args, **kwargs)
            except requests.exceptions.ConnectionError:
                logging.warning(
                    f'Connection to Auth API failed - {request.url}'
                )
                raise ServiceUnavailable('Can not verify token, retry again!')

        return view

    return decorator


def insession(f):
    '''Decorator for session verification, may raise Forbidden exception'''
    @wraps(f)
    def decorated(*args, **kwargs):
        # No auth during testing
        if __debug_mode():
            return f(*args, **kwargs)
        error = init_jwt_session()
        active_time_span = datetime.datetime.now().timestamp() - session.get(
            LAST_ACTIVE,
            datetime.datetime.now().timestamp()
        )

        if active_time_span > SESSION_EXPIRY_SECONDS:
            # Clear the session because session expired
            authcli.clear_session()
            raise Forbidden('Login required-Session expired')
        else:
            session.pop(LAST_ACTIVE, None)
            session[LAST_ACTIVE] = datetime.datetime.now().timestamp()

        if SKEY_USER not in session:
            # init jwt session resulted an error
            if error != '':
                return redirect(f'/login?error={error}')

            access_token = request.headers.get(HKEY_AUTH, '').replace(
                'Bearer ', '')
            if access_token != '':
                username = authcli.verify_token(access_token)
                authcli.init_session(username=username,
                                     access_token=access_token)
            else:
                raise Forbidden('Login required')

        token_time_span = datetime.datetime.now().timestamp() - session.get(
            TOKEN_START_TIME,
            datetime.datetime.now().timestamp()
        )
        if token_time_span > TOKEN_EXPIRY_SECONDS:
            # Clear the session because session expired
            authcli.refresh_access_token()
        # remove jwt from query string if it is there
        if request.args.get('jwt', '') != '':
            return redirect(remove_jwt_from_url(request.url))

        return f(*args, **kwargs)
    return decorated
