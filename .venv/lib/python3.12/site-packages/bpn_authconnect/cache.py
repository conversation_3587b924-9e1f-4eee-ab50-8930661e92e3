import time
import logging
from functools import wraps
import bpn_authconnect

# Define a cache dictionary
__cache_store = dict()


# Define a decorator to handle caching
def cache_result(expiry=60):
    """
    Decorator to cache a function's result for a given period of time (expiry in seconds).
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # Create a key based on the function arguments
            keyargs = args
            if (type(args[0]) is bpn_authconnect.AuthCli):
                keyargs = args[1:]
            key = (func.__name__, keyargs, tuple(sorted(kwargs.items())))
            # Check if the key is in the cache and not expired
            if key in __cache_store and \
                    __cache_store[key]['expiration'] > time.time():
                logging.info(
                    f"Cache Hit {__cache_store[key]['expiration'] - time.time()}s to expire"
                )
                return __cache_store[key]['value']

            logging.info(f"Cache missed, calling {func.__name__}")
            # If the key is not in the cache or expired, call the function
            result = func(*args, **kwargs)
            expiration = time.time() + expiry
            __cache_store[key] = {'value': result, 'expiration': expiration}
            return result

        return wrapper
    return decorator
