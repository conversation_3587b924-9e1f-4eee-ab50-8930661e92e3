Metadata-Version: 2.1
Name: bpn-authconnect
Version: 3.1.4
Summary: A client for the authentication api.
Home-page: https://git.biome.io/platform/auth-cli
Author: <PERSON>
Author-email: <EMAIL>
License: Proprietary (Biome Analytics)
Platform: UNKNOWN
Description-Content-Type: text/markdown
Requires-Dist: azure-keyvault-secrets>=4.2.0
Requires-Dist: azure-identity>=1.5.0
Requires-Dist: Flask>=1.1.1
Requires-Dist: requests>=2.22.0
Requires-Dist: Werkzeug>=0.16.0
Requires-Dist: pyjwt>=2.3.0
Requires-Dist: graphqlclient~=0.2.4
Requires-Dist: backoff~=2.2.1

# Auth Client

How to use this client?


Set API url as environment variable
----------

    export BPN_URL=https://bifrost.dev.biomedata.io


Other environment variables
----------
VAULT_NAME=`biome-apps-keyvault`


Use authenticate decorator
----------

    from flask import g
    from bpn_authconnect import authenticate
    
    @app.get('/resource')
    @authenticate
    def service_requires_auth():
        if g.user in admins:
            return redirect(url_for("admin_view"))
        send_regular_response()
    
returnUrl
----------

    from bpn_authconnect import RETURN_URL_KEY 
    request.args[RETURN_URL_KEY] = redirectUrl


