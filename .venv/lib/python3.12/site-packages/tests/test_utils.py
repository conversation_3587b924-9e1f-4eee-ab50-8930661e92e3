# -*- coding: utf-8 -*-
"""
Created on 2020-03-24

@author: khalid
"""

import os
import unittest as ut
import bpn_authconnect as auth


class TestCliUtil(ut.TestCase):

    def setUp(self):
        self.url = 'https://bifrost.dev.biomedata.io'
        os.environ['BPN_URL'] = self.url
        print(os.getenv('BPN_URL'))

    def test_apiurl(self):
        authcli = auth.AuthCli()
        uri = authcli.api_url
        self.assertTrue(uri.startswith(self.url))

    def test_bearer_header(self):
        headers = auth.bearer_header('randomtokena')
        self.assertEqual(len(headers), 1)
        self.assertEqual(headers[auth.HKEY_AUTH], 'Bearer randomtokena')

    def tearDown(self):
        pass


if __name__ == '__main__':
    ut.main()
