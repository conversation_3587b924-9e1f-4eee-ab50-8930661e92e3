import os
import unittest as ut
from http import HTTPStatus
import requests
from requests.auth import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from flask import Flask, session
import bpn_authconnect as auth
from unittest.mock import patch, Mock
from bpn_authconnect import authcli, Forbidden
from bpn_authconnect.sso import bp
from azure.identity import DefaultAzureCredential
from azure.keyvault.secrets import SecretClient


def basic_auth(user, passw):
    basicauth = HTTPBasicAuth(user, passw)
    r = requests.Request()
    basicauth(r)
    return r.headers


# Create flask app
app = Flask(__name__)
app.secret_key = os.urandom(16)
app.register_blueprint(bp)


def req_get_mock():
    req_get_mock = Mock()
    req_get_mock.json = Mock(return_value={"accessToken": "new_access_token"})
    req_get_mock.status_code = 200
    return req_get_mock


def secret_client_mock():
    client = Mock(spec=SecretClient)
    secret = Mock()
    secret.value = "secret_value"
    client.get_secret = Mock(return_value=secret)
    return client


@app.route('/auth1', methods=['GET'])
@auth.authenticate()
def auth1():
    """ First route without group membership """
    return 'OK'


@app.route('/auth2', methods=['GET'])
@auth.authenticate(privilege='VPN')
def auth2():
    """ Second route with group membership """
    return 'OK'


class TestApp(ut.TestCase):

    def setUp(self):
        self.url = 'https://bifrost.dev.biomedata.io'
        os.environ['BPN_URL'] = self.url

    def test_logout(self):
        with app.test_request_context('/logout'):
            self.assertFalse(auth.SKEY_USER in session)

    @patch.object(authcli, "verify_jwt", return_value="user")
    @patch('requests.get', return_value=req_get_mock())  # Patch requests.get
    def test_auth_session(self, m_request_get, m_verify_jwt):
        with app.test_client() as c:
            # Login to create a session
            c.get(
                '/callback?jwt={"accessToken": "access", "refreshToken": "refresh"}')
            # Use existing session for auth - NO token
            resp = c.get('/auth1')
            self.assertEqual(resp.status_code, HTTPStatus.OK)
            authcli.refresh_access_token()
            m_request_get.return_value.status_code = 403
            with self.assertRaises(Forbidden):
                authcli.refresh_access_token()

    def test_login_error(self):
        with app.test_client() as c:
            # Login to create a session
            response = c.get('/callback?error=User not found')
            self.assertEqual(response.status_code, 302)
            self.assertEqual(
                '/login?error=User%20not%20found',
                response.headers.get('location')
            )

    def test_no_auth(self):
        with app.test_client() as c:
            # Destroy if any session
            c.get('/logout')
            # Test auth
            resp = c.get('/auth1')
            self.assertEqual(resp.status_code, HTTPStatus.UNAUTHORIZED)

    @patch.object(authcli, "verify_jwt", return_value="user")
    @patch.object(authcli, "check_privilege", return_value=True)
    def test_auth_group(self, *mocks):
        with app.test_client() as c:
            # Login to create a session
            c.get(
                '/callback?jwt={"accessToken": "access", "refreshToken": "refresh"}'
            )
            # Test auth
            resp = c.get('/auth2')
            self.assertEqual(resp.status_code, HTTPStatus.OK)

    @patch(
        'bpn_authconnect.SecretClient',
        return_value=secret_client_mock()
    )
    @patch(
        "bpn_authconnect.serialization.load_pem_public_key",
        return_value="public_key_str"
    )
    @patch(
        'bpn_authconnect.DefaultAzureCredential',
        return_value=Mock(spec=DefaultAzureCredential)
    )
    def test_authcli_secret(self, *mocks):
        self.assertEqual(authcli.secret, "public_key_str")

    def tearDown(self):
        pass


if __name__ == '__main__':
    ut.main()
