import unittest
from bpn_authconnect import remove_jwt_from_url


class TestInit(unittest.TestCase):

    def test_remove_jwt_from_url_with_jwt(self):
        url = "https://example.com/path?jwt=%7B%22accessToken%22%3A%22ey%22%2C%22refreshToken%22%3A%22a&param1=value1"
        expected_url = "https://example.com/path?param1=value1"
        self.assertEqual(remove_jwt_from_url(url), expected_url)

    def test_remove_jwt_from_url_without_jwt(self):
        url = "https://example.com/path?param1=value1&param2=value2"
        self.assertEqual(remove_jwt_from_url(url), url)

    def test_remove_jwt_from_url_only_jwt(self):
        url = "https://example.com/path?jwt=%7B%22accessToken%22%3A%22ey%22%2C%22refreshToken%22%3A%22a"
        expected_url = "https://example.com/path"
        self.assertEqual(remove_jwt_from_url(url), expected_url)

    def test_remove_jwt_from_url_multiple_jwt(self):
        url = "https://example.com/path?jwt=abc123&param1=value1&jwt=def456"
        expected_url = "https://example.com/path?param1=value1"
        self.assertEqual(remove_jwt_from_url(url), expected_url)

    def test_remove_jwt_from_url_no_query_params(self):
        url = "https://example.com/path"
        self.assertEqual(remove_jwt_from_url(url), url)

    def test_remove_jwt_from_url_with_fragment(self):
        url = "https://example.com/path?jwt=abc123&param1=value1#fragment"
        expected_url = "https://example.com/path?param1=value1#fragment"
        self.assertEqual(remove_jwt_from_url(url), expected_url)


if __name__ == '__main__':
    unittest.main()
