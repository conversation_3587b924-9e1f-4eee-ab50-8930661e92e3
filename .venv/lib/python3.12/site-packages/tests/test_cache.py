import unittest
import time
from unittest.mock import Mock
import bpn_authconnect


class TestCacheResult(unittest.TestCase):
    def setUp(self):
        self.mock_func = Mock()
        self.mock_authcli = Mock(spec=bpn_authconnect.AuthCli)

    def test_cache_miss(self):
        @bpn_authconnect.cache_result(expiry=5)
        def test_func(arg1, arg2):
            return self.mock_func(arg1, arg2)

        result = test_func(1, 2)
        self.mock_func.assert_called_once_with(1, 2)
        self.assertEqual(result, self.mock_func.return_value)

    def test_cache_hit(self):
        @bpn_authconnect.cache_result(expiry=5)
        def test_func(arg1, arg2):
            return self.mock_func(arg1, arg2)

        test_func(3, 4)
        self.mock_func.reset_mock()
        result = test_func(3, 4)
        self.mock_func.assert_not_called()
        self.assertEqual(result, self.mock_func.return_value)

    def test_cache_expiration(self):
        @bpn_authconnect.cache_result(expiry=1)
        def test_func(arg1, arg2):
            return self.mock_func(arg1, arg2)

        test_func(5, 6)
        self.mock_func.reset_mock()
        time.sleep(2)
        result = test_func(5, 6)
        self.mock_func.assert_called_once_with(5, 6)
        self.assertEqual(result, self.mock_func.return_value)

    def test_cache_with_authcli(self):
        @bpn_authconnect.cache_result(expiry=5)
        def test_func(authcli, arg1, arg2):
            return self.mock_func(authcli, arg1, arg2)

        result = test_func(self.mock_authcli, 7, 8)
        self.mock_func.assert_called_once_with(self.mock_authcli, 7, 8)
        self.assertEqual(result, self.mock_func.return_value)


if __name__ == '__main__':
    unittest.main()
