# coding: utf-8

"""
    Kubernetes

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)  # noqa: E501

    The version of the OpenAPI document: release-1.33
    Generated by: https://openapi-generator.tech
"""


import pprint
import re  # noqa: F401

import six

from kubernetes.client.configuration import Configuration


class V1alpha1StorageVersionMigrationSpec(object):
    """NOTE: This class is auto generated by OpenAPI Generator.
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    """
    Attributes:
      openapi_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    openapi_types = {
        'continue_token': 'str',
        'resource': 'V1alpha1GroupVersionResource'
    }

    attribute_map = {
        'continue_token': 'continueToken',
        'resource': 'resource'
    }

    def __init__(self, continue_token=None, resource=None, local_vars_configuration=None):  # noqa: E501
        """V1alpha1StorageVersionMigrationSpec - a model defined in OpenAPI"""  # noqa: E501
        if local_vars_configuration is None:
            local_vars_configuration = Configuration()
        self.local_vars_configuration = local_vars_configuration

        self._continue_token = None
        self._resource = None
        self.discriminator = None

        if continue_token is not None:
            self.continue_token = continue_token
        self.resource = resource

    @property
    def continue_token(self):
        """Gets the continue_token of this V1alpha1StorageVersionMigrationSpec.  # noqa: E501

        The token used in the list options to get the next chunk of objects to migrate. When the .status.conditions indicates the migration is \"Running\", users can use this token to check the progress of the migration.  # noqa: E501

        :return: The continue_token of this V1alpha1StorageVersionMigrationSpec.  # noqa: E501
        :rtype: str
        """
        return self._continue_token

    @continue_token.setter
    def continue_token(self, continue_token):
        """Sets the continue_token of this V1alpha1StorageVersionMigrationSpec.

        The token used in the list options to get the next chunk of objects to migrate. When the .status.conditions indicates the migration is \"Running\", users can use this token to check the progress of the migration.  # noqa: E501

        :param continue_token: The continue_token of this V1alpha1StorageVersionMigrationSpec.  # noqa: E501
        :type: str
        """

        self._continue_token = continue_token

    @property
    def resource(self):
        """Gets the resource of this V1alpha1StorageVersionMigrationSpec.  # noqa: E501


        :return: The resource of this V1alpha1StorageVersionMigrationSpec.  # noqa: E501
        :rtype: V1alpha1GroupVersionResource
        """
        return self._resource

    @resource.setter
    def resource(self, resource):
        """Sets the resource of this V1alpha1StorageVersionMigrationSpec.


        :param resource: The resource of this V1alpha1StorageVersionMigrationSpec.  # noqa: E501
        :type: V1alpha1GroupVersionResource
        """
        if self.local_vars_configuration.client_side_validation and resource is None:  # noqa: E501
            raise ValueError("Invalid value for `resource`, must not be `None`")  # noqa: E501

        self._resource = resource

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.openapi_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, V1alpha1StorageVersionMigrationSpec):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, V1alpha1StorageVersionMigrationSpec):
            return True

        return self.to_dict() != other.to_dict()
