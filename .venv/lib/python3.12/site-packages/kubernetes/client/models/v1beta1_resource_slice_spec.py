# coding: utf-8

"""
    Kubernetes

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)  # noqa: E501

    The version of the OpenAPI document: release-1.33
    Generated by: https://openapi-generator.tech
"""


import pprint
import re  # noqa: F401

import six

from kubernetes.client.configuration import Configuration


class V1beta1ResourceSliceSpec(object):
    """NOTE: This class is auto generated by OpenAPI Generator.
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    """
    Attributes:
      openapi_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    openapi_types = {
        'all_nodes': 'bool',
        'devices': 'list[V1beta1Device]',
        'driver': 'str',
        'node_name': 'str',
        'node_selector': 'V1NodeSelector',
        'per_device_node_selection': 'bool',
        'pool': 'V1beta1ResourcePool',
        'shared_counters': 'list[V1beta1CounterSet]'
    }

    attribute_map = {
        'all_nodes': 'allNodes',
        'devices': 'devices',
        'driver': 'driver',
        'node_name': 'nodeName',
        'node_selector': 'nodeSelector',
        'per_device_node_selection': 'perDeviceNodeSelection',
        'pool': 'pool',
        'shared_counters': 'sharedCounters'
    }

    def __init__(self, all_nodes=None, devices=None, driver=None, node_name=None, node_selector=None, per_device_node_selection=None, pool=None, shared_counters=None, local_vars_configuration=None):  # noqa: E501
        """V1beta1ResourceSliceSpec - a model defined in OpenAPI"""  # noqa: E501
        if local_vars_configuration is None:
            local_vars_configuration = Configuration()
        self.local_vars_configuration = local_vars_configuration

        self._all_nodes = None
        self._devices = None
        self._driver = None
        self._node_name = None
        self._node_selector = None
        self._per_device_node_selection = None
        self._pool = None
        self._shared_counters = None
        self.discriminator = None

        if all_nodes is not None:
            self.all_nodes = all_nodes
        if devices is not None:
            self.devices = devices
        self.driver = driver
        if node_name is not None:
            self.node_name = node_name
        if node_selector is not None:
            self.node_selector = node_selector
        if per_device_node_selection is not None:
            self.per_device_node_selection = per_device_node_selection
        self.pool = pool
        if shared_counters is not None:
            self.shared_counters = shared_counters

    @property
    def all_nodes(self):
        """Gets the all_nodes of this V1beta1ResourceSliceSpec.  # noqa: E501

        AllNodes indicates that all nodes have access to the resources in the pool.  Exactly one of NodeName, NodeSelector, AllNodes, and PerDeviceNodeSelection must be set.  # noqa: E501

        :return: The all_nodes of this V1beta1ResourceSliceSpec.  # noqa: E501
        :rtype: bool
        """
        return self._all_nodes

    @all_nodes.setter
    def all_nodes(self, all_nodes):
        """Sets the all_nodes of this V1beta1ResourceSliceSpec.

        AllNodes indicates that all nodes have access to the resources in the pool.  Exactly one of NodeName, NodeSelector, AllNodes, and PerDeviceNodeSelection must be set.  # noqa: E501

        :param all_nodes: The all_nodes of this V1beta1ResourceSliceSpec.  # noqa: E501
        :type: bool
        """

        self._all_nodes = all_nodes

    @property
    def devices(self):
        """Gets the devices of this V1beta1ResourceSliceSpec.  # noqa: E501

        Devices lists some or all of the devices in this pool.  Must not have more than 128 entries.  # noqa: E501

        :return: The devices of this V1beta1ResourceSliceSpec.  # noqa: E501
        :rtype: list[V1beta1Device]
        """
        return self._devices

    @devices.setter
    def devices(self, devices):
        """Sets the devices of this V1beta1ResourceSliceSpec.

        Devices lists some or all of the devices in this pool.  Must not have more than 128 entries.  # noqa: E501

        :param devices: The devices of this V1beta1ResourceSliceSpec.  # noqa: E501
        :type: list[V1beta1Device]
        """

        self._devices = devices

    @property
    def driver(self):
        """Gets the driver of this V1beta1ResourceSliceSpec.  # noqa: E501

        Driver identifies the DRA driver providing the capacity information. A field selector can be used to list only ResourceSlice objects with a certain driver name.  Must be a DNS subdomain and should end with a DNS domain owned by the vendor of the driver. This field is immutable.  # noqa: E501

        :return: The driver of this V1beta1ResourceSliceSpec.  # noqa: E501
        :rtype: str
        """
        return self._driver

    @driver.setter
    def driver(self, driver):
        """Sets the driver of this V1beta1ResourceSliceSpec.

        Driver identifies the DRA driver providing the capacity information. A field selector can be used to list only ResourceSlice objects with a certain driver name.  Must be a DNS subdomain and should end with a DNS domain owned by the vendor of the driver. This field is immutable.  # noqa: E501

        :param driver: The driver of this V1beta1ResourceSliceSpec.  # noqa: E501
        :type: str
        """
        if self.local_vars_configuration.client_side_validation and driver is None:  # noqa: E501
            raise ValueError("Invalid value for `driver`, must not be `None`")  # noqa: E501

        self._driver = driver

    @property
    def node_name(self):
        """Gets the node_name of this V1beta1ResourceSliceSpec.  # noqa: E501

        NodeName identifies the node which provides the resources in this pool. A field selector can be used to list only ResourceSlice objects belonging to a certain node.  This field can be used to limit access from nodes to ResourceSlices with the same node name. It also indicates to autoscalers that adding new nodes of the same type as some old node might also make new resources available.  Exactly one of NodeName, NodeSelector, AllNodes, and PerDeviceNodeSelection must be set. This field is immutable.  # noqa: E501

        :return: The node_name of this V1beta1ResourceSliceSpec.  # noqa: E501
        :rtype: str
        """
        return self._node_name

    @node_name.setter
    def node_name(self, node_name):
        """Sets the node_name of this V1beta1ResourceSliceSpec.

        NodeName identifies the node which provides the resources in this pool. A field selector can be used to list only ResourceSlice objects belonging to a certain node.  This field can be used to limit access from nodes to ResourceSlices with the same node name. It also indicates to autoscalers that adding new nodes of the same type as some old node might also make new resources available.  Exactly one of NodeName, NodeSelector, AllNodes, and PerDeviceNodeSelection must be set. This field is immutable.  # noqa: E501

        :param node_name: The node_name of this V1beta1ResourceSliceSpec.  # noqa: E501
        :type: str
        """

        self._node_name = node_name

    @property
    def node_selector(self):
        """Gets the node_selector of this V1beta1ResourceSliceSpec.  # noqa: E501


        :return: The node_selector of this V1beta1ResourceSliceSpec.  # noqa: E501
        :rtype: V1NodeSelector
        """
        return self._node_selector

    @node_selector.setter
    def node_selector(self, node_selector):
        """Sets the node_selector of this V1beta1ResourceSliceSpec.


        :param node_selector: The node_selector of this V1beta1ResourceSliceSpec.  # noqa: E501
        :type: V1NodeSelector
        """

        self._node_selector = node_selector

    @property
    def per_device_node_selection(self):
        """Gets the per_device_node_selection of this V1beta1ResourceSliceSpec.  # noqa: E501

        PerDeviceNodeSelection defines whether the access from nodes to resources in the pool is set on the ResourceSlice level or on each device. If it is set to true, every device defined the ResourceSlice must specify this individually.  Exactly one of NodeName, NodeSelector, AllNodes, and PerDeviceNodeSelection must be set.  # noqa: E501

        :return: The per_device_node_selection of this V1beta1ResourceSliceSpec.  # noqa: E501
        :rtype: bool
        """
        return self._per_device_node_selection

    @per_device_node_selection.setter
    def per_device_node_selection(self, per_device_node_selection):
        """Sets the per_device_node_selection of this V1beta1ResourceSliceSpec.

        PerDeviceNodeSelection defines whether the access from nodes to resources in the pool is set on the ResourceSlice level or on each device. If it is set to true, every device defined the ResourceSlice must specify this individually.  Exactly one of NodeName, NodeSelector, AllNodes, and PerDeviceNodeSelection must be set.  # noqa: E501

        :param per_device_node_selection: The per_device_node_selection of this V1beta1ResourceSliceSpec.  # noqa: E501
        :type: bool
        """

        self._per_device_node_selection = per_device_node_selection

    @property
    def pool(self):
        """Gets the pool of this V1beta1ResourceSliceSpec.  # noqa: E501


        :return: The pool of this V1beta1ResourceSliceSpec.  # noqa: E501
        :rtype: V1beta1ResourcePool
        """
        return self._pool

    @pool.setter
    def pool(self, pool):
        """Sets the pool of this V1beta1ResourceSliceSpec.


        :param pool: The pool of this V1beta1ResourceSliceSpec.  # noqa: E501
        :type: V1beta1ResourcePool
        """
        if self.local_vars_configuration.client_side_validation and pool is None:  # noqa: E501
            raise ValueError("Invalid value for `pool`, must not be `None`")  # noqa: E501

        self._pool = pool

    @property
    def shared_counters(self):
        """Gets the shared_counters of this V1beta1ResourceSliceSpec.  # noqa: E501

        SharedCounters defines a list of counter sets, each of which has a name and a list of counters available.  The names of the SharedCounters must be unique in the ResourceSlice.  The maximum number of SharedCounters is 32.  # noqa: E501

        :return: The shared_counters of this V1beta1ResourceSliceSpec.  # noqa: E501
        :rtype: list[V1beta1CounterSet]
        """
        return self._shared_counters

    @shared_counters.setter
    def shared_counters(self, shared_counters):
        """Sets the shared_counters of this V1beta1ResourceSliceSpec.

        SharedCounters defines a list of counter sets, each of which has a name and a list of counters available.  The names of the SharedCounters must be unique in the ResourceSlice.  The maximum number of SharedCounters is 32.  # noqa: E501

        :param shared_counters: The shared_counters of this V1beta1ResourceSliceSpec.  # noqa: E501
        :type: list[V1beta1CounterSet]
        """

        self._shared_counters = shared_counters

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.openapi_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, V1beta1ResourceSliceSpec):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, V1beta1ResourceSliceSpec):
            return True

        return self.to_dict() != other.to_dict()
