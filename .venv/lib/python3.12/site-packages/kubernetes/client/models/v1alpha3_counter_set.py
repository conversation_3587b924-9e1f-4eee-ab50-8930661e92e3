# coding: utf-8

"""
    Kubernetes

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)  # noqa: E501

    The version of the OpenAPI document: release-1.33
    Generated by: https://openapi-generator.tech
"""


import pprint
import re  # noqa: F401

import six

from kubernetes.client.configuration import Configuration


class V1alpha3CounterSet(object):
    """NOTE: This class is auto generated by OpenAPI Generator.
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    """
    Attributes:
      openapi_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    openapi_types = {
        'counters': 'dict(str, V1alpha3Counter)',
        'name': 'str'
    }

    attribute_map = {
        'counters': 'counters',
        'name': 'name'
    }

    def __init__(self, counters=None, name=None, local_vars_configuration=None):  # noqa: E501
        """V1alpha3CounterSet - a model defined in OpenAPI"""  # noqa: E501
        if local_vars_configuration is None:
            local_vars_configuration = Configuration()
        self.local_vars_configuration = local_vars_configuration

        self._counters = None
        self._name = None
        self.discriminator = None

        self.counters = counters
        self.name = name

    @property
    def counters(self):
        """Gets the counters of this V1alpha3CounterSet.  # noqa: E501

        Counters defines the counters that will be consumed by the device. The name of each counter must be unique in that set and must be a DNS label.  To ensure this uniqueness, capacities defined by the vendor must be listed without the driver name as domain prefix in their name. All others must be listed with their domain prefix.  The maximum number of counters is 32.  # noqa: E501

        :return: The counters of this V1alpha3CounterSet.  # noqa: E501
        :rtype: dict(str, V1alpha3Counter)
        """
        return self._counters

    @counters.setter
    def counters(self, counters):
        """Sets the counters of this V1alpha3CounterSet.

        Counters defines the counters that will be consumed by the device. The name of each counter must be unique in that set and must be a DNS label.  To ensure this uniqueness, capacities defined by the vendor must be listed without the driver name as domain prefix in their name. All others must be listed with their domain prefix.  The maximum number of counters is 32.  # noqa: E501

        :param counters: The counters of this V1alpha3CounterSet.  # noqa: E501
        :type: dict(str, V1alpha3Counter)
        """
        if self.local_vars_configuration.client_side_validation and counters is None:  # noqa: E501
            raise ValueError("Invalid value for `counters`, must not be `None`")  # noqa: E501

        self._counters = counters

    @property
    def name(self):
        """Gets the name of this V1alpha3CounterSet.  # noqa: E501

        CounterSet is the name of the set from which the counters defined will be consumed.  # noqa: E501

        :return: The name of this V1alpha3CounterSet.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this V1alpha3CounterSet.

        CounterSet is the name of the set from which the counters defined will be consumed.  # noqa: E501

        :param name: The name of this V1alpha3CounterSet.  # noqa: E501
        :type: str
        """
        if self.local_vars_configuration.client_side_validation and name is None:  # noqa: E501
            raise ValueError("Invalid value for `name`, must not be `None`")  # noqa: E501

        self._name = name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.openapi_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, V1alpha3CounterSet):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, V1alpha3CounterSet):
            return True

        return self.to_dict() != other.to_dict()
