# coding: utf-8

"""
    Kubernetes

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)  # noqa: E501

    The version of the OpenAPI document: release-1.33
    Generated by: https://openapi-generator.tech
"""


import pprint
import re  # noqa: F401

import six

from kubernetes.client.configuration import Configuration


class V1alpha3BasicDevice(object):
    """NOTE: This class is auto generated by OpenAPI Generator.
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    """
    Attributes:
      openapi_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    openapi_types = {
        'all_nodes': 'bool',
        'attributes': 'dict(str, V1alpha3DeviceAttribute)',
        'capacity': 'dict(str, str)',
        'consumes_counters': 'list[V1alpha3DeviceCounterConsumption]',
        'node_name': 'str',
        'node_selector': 'V1NodeSelector',
        'taints': 'list[V1alpha3DeviceTaint]'
    }

    attribute_map = {
        'all_nodes': 'allNodes',
        'attributes': 'attributes',
        'capacity': 'capacity',
        'consumes_counters': 'consumesCounters',
        'node_name': 'nodeName',
        'node_selector': 'nodeSelector',
        'taints': 'taints'
    }

    def __init__(self, all_nodes=None, attributes=None, capacity=None, consumes_counters=None, node_name=None, node_selector=None, taints=None, local_vars_configuration=None):  # noqa: E501
        """V1alpha3BasicDevice - a model defined in OpenAPI"""  # noqa: E501
        if local_vars_configuration is None:
            local_vars_configuration = Configuration()
        self.local_vars_configuration = local_vars_configuration

        self._all_nodes = None
        self._attributes = None
        self._capacity = None
        self._consumes_counters = None
        self._node_name = None
        self._node_selector = None
        self._taints = None
        self.discriminator = None

        if all_nodes is not None:
            self.all_nodes = all_nodes
        if attributes is not None:
            self.attributes = attributes
        if capacity is not None:
            self.capacity = capacity
        if consumes_counters is not None:
            self.consumes_counters = consumes_counters
        if node_name is not None:
            self.node_name = node_name
        if node_selector is not None:
            self.node_selector = node_selector
        if taints is not None:
            self.taints = taints

    @property
    def all_nodes(self):
        """Gets the all_nodes of this V1alpha3BasicDevice.  # noqa: E501

        AllNodes indicates that all nodes have access to the device.  Must only be set if Spec.PerDeviceNodeSelection is set to true. At most one of NodeName, NodeSelector and AllNodes can be set.  # noqa: E501

        :return: The all_nodes of this V1alpha3BasicDevice.  # noqa: E501
        :rtype: bool
        """
        return self._all_nodes

    @all_nodes.setter
    def all_nodes(self, all_nodes):
        """Sets the all_nodes of this V1alpha3BasicDevice.

        AllNodes indicates that all nodes have access to the device.  Must only be set if Spec.PerDeviceNodeSelection is set to true. At most one of NodeName, NodeSelector and AllNodes can be set.  # noqa: E501

        :param all_nodes: The all_nodes of this V1alpha3BasicDevice.  # noqa: E501
        :type: bool
        """

        self._all_nodes = all_nodes

    @property
    def attributes(self):
        """Gets the attributes of this V1alpha3BasicDevice.  # noqa: E501

        Attributes defines the set of attributes for this device. The name of each attribute must be unique in that set.  The maximum number of attributes and capacities combined is 32.  # noqa: E501

        :return: The attributes of this V1alpha3BasicDevice.  # noqa: E501
        :rtype: dict(str, V1alpha3DeviceAttribute)
        """
        return self._attributes

    @attributes.setter
    def attributes(self, attributes):
        """Sets the attributes of this V1alpha3BasicDevice.

        Attributes defines the set of attributes for this device. The name of each attribute must be unique in that set.  The maximum number of attributes and capacities combined is 32.  # noqa: E501

        :param attributes: The attributes of this V1alpha3BasicDevice.  # noqa: E501
        :type: dict(str, V1alpha3DeviceAttribute)
        """

        self._attributes = attributes

    @property
    def capacity(self):
        """Gets the capacity of this V1alpha3BasicDevice.  # noqa: E501

        Capacity defines the set of capacities for this device. The name of each capacity must be unique in that set.  The maximum number of attributes and capacities combined is 32.  # noqa: E501

        :return: The capacity of this V1alpha3BasicDevice.  # noqa: E501
        :rtype: dict(str, str)
        """
        return self._capacity

    @capacity.setter
    def capacity(self, capacity):
        """Sets the capacity of this V1alpha3BasicDevice.

        Capacity defines the set of capacities for this device. The name of each capacity must be unique in that set.  The maximum number of attributes and capacities combined is 32.  # noqa: E501

        :param capacity: The capacity of this V1alpha3BasicDevice.  # noqa: E501
        :type: dict(str, str)
        """

        self._capacity = capacity

    @property
    def consumes_counters(self):
        """Gets the consumes_counters of this V1alpha3BasicDevice.  # noqa: E501

        ConsumesCounters defines a list of references to sharedCounters and the set of counters that the device will consume from those counter sets.  There can only be a single entry per counterSet.  The total number of device counter consumption entries must be <= 32. In addition, the total number in the entire ResourceSlice must be <= 1024 (for example, 64 devices with 16 counters each).  # noqa: E501

        :return: The consumes_counters of this V1alpha3BasicDevice.  # noqa: E501
        :rtype: list[V1alpha3DeviceCounterConsumption]
        """
        return self._consumes_counters

    @consumes_counters.setter
    def consumes_counters(self, consumes_counters):
        """Sets the consumes_counters of this V1alpha3BasicDevice.

        ConsumesCounters defines a list of references to sharedCounters and the set of counters that the device will consume from those counter sets.  There can only be a single entry per counterSet.  The total number of device counter consumption entries must be <= 32. In addition, the total number in the entire ResourceSlice must be <= 1024 (for example, 64 devices with 16 counters each).  # noqa: E501

        :param consumes_counters: The consumes_counters of this V1alpha3BasicDevice.  # noqa: E501
        :type: list[V1alpha3DeviceCounterConsumption]
        """

        self._consumes_counters = consumes_counters

    @property
    def node_name(self):
        """Gets the node_name of this V1alpha3BasicDevice.  # noqa: E501

        NodeName identifies the node where the device is available.  Must only be set if Spec.PerDeviceNodeSelection is set to true. At most one of NodeName, NodeSelector and AllNodes can be set.  # noqa: E501

        :return: The node_name of this V1alpha3BasicDevice.  # noqa: E501
        :rtype: str
        """
        return self._node_name

    @node_name.setter
    def node_name(self, node_name):
        """Sets the node_name of this V1alpha3BasicDevice.

        NodeName identifies the node where the device is available.  Must only be set if Spec.PerDeviceNodeSelection is set to true. At most one of NodeName, NodeSelector and AllNodes can be set.  # noqa: E501

        :param node_name: The node_name of this V1alpha3BasicDevice.  # noqa: E501
        :type: str
        """

        self._node_name = node_name

    @property
    def node_selector(self):
        """Gets the node_selector of this V1alpha3BasicDevice.  # noqa: E501


        :return: The node_selector of this V1alpha3BasicDevice.  # noqa: E501
        :rtype: V1NodeSelector
        """
        return self._node_selector

    @node_selector.setter
    def node_selector(self, node_selector):
        """Sets the node_selector of this V1alpha3BasicDevice.


        :param node_selector: The node_selector of this V1alpha3BasicDevice.  # noqa: E501
        :type: V1NodeSelector
        """

        self._node_selector = node_selector

    @property
    def taints(self):
        """Gets the taints of this V1alpha3BasicDevice.  # noqa: E501

        If specified, these are the driver-defined taints.  The maximum number of taints is 4.  This is an alpha field and requires enabling the DRADeviceTaints feature gate.  # noqa: E501

        :return: The taints of this V1alpha3BasicDevice.  # noqa: E501
        :rtype: list[V1alpha3DeviceTaint]
        """
        return self._taints

    @taints.setter
    def taints(self, taints):
        """Sets the taints of this V1alpha3BasicDevice.

        If specified, these are the driver-defined taints.  The maximum number of taints is 4.  This is an alpha field and requires enabling the DRADeviceTaints feature gate.  # noqa: E501

        :param taints: The taints of this V1alpha3BasicDevice.  # noqa: E501
        :type: list[V1alpha3DeviceTaint]
        """

        self._taints = taints

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.openapi_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, V1alpha3BasicDevice):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, V1alpha3BasicDevice):
            return True

        return self.to_dict() != other.to_dict()
